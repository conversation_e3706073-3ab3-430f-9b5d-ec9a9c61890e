/**
 * Authentication mocking utilities for testing
 * 
 * Provides utilities to mock Clerk authentication in tests
 */

import { vi } from 'vitest'

// Mock user data for testing
export interface MockUser {
  id: string
  email?: string
  name?: string
  avatar?: string
}

// Global mock state
let mockAuthState: {
  isAuthenticated: boolean
  currentUser: MockUser | null
} = {
  isAuthenticated: false,
  currentUser: null,
}

/**
 * Setup authentication mocks
 */
export function setupMockAuth() {
  console.log('🔧 Setting up authentication mocks...')

  // Mock Clerk's auth function
  vi.mock('@clerk/nextjs/server', async () => {
    const actual = await vi.importActual('@clerk/nextjs/server')
    return {
      ...actual,
      auth: vi.fn(() => ({
        userId: mockAuthState.currentUser?.id || null,
        user: mockAuthState.currentUser,
      })),
      clerkClient: {
        users: {
          getUser: vi.fn((userId: string) => {
            if (mockAuthState.currentUser?.id === userId) {
              return Promise.resolve({
                id: userId,
                emailAddresses: mockAuthState.currentUser.email ?
                  [{ emailAddress: mockAuthState.currentUser.email }] : [],
                firstName: mockAuthState.currentUser.name?.split(' ')[0] || null,
                lastName: mockAuthState.currentUser.name?.split(' ')[1] || null,
                imageUrl: mockAuthState.currentUser.avatar || null,
              })
            }
            throw new Error('User not found')
          }),
        },
      },
    }
  })

  console.log('✅ Authentication mocks configured')
}

/**
 * Mock authenticated user for tests
 */
export function mockAuthenticatedUser(user: MockUser) {
  console.log(`🔐 Mocking authenticated user: ${user.id}`)

  mockAuthState.isAuthenticated = true
  mockAuthState.currentUser = user

  // The mock implementation will use the updated state
  console.log(`✅ Mock auth state updated for user: ${user.id}`)
}

/**
 * Mock unauthenticated state for tests
 */
export function mockUnauthenticatedUser() {
  console.log('🚫 Mocking unauthenticated state')

  mockAuthState.isAuthenticated = false
  mockAuthState.currentUser = null

  console.log('✅ Mock auth state reset to unauthenticated')
}

/**
 * Get current mock auth state
 */
export function getMockAuthState() {
  return { ...mockAuthState }
}

/**
 * Reset authentication mocks
 */
export function resetMockAuth() {
  console.log('🔄 Resetting authentication mocks')

  mockAuthState.isAuthenticated = false
  mockAuthState.currentUser = null

  console.log('✅ Mock auth state reset')
}

/**
 * Cleanup authentication mocks
 */
export function cleanupMockAuth() {
  console.log('🧹 Cleaning up authentication mocks')
  
  // Reset state
  resetMockAuth()
  
  // Clear all mocks
  vi.clearAllMocks()
}

/**
 * Create test context with authenticated user
 */
export function createTestContext(user?: MockUser) {
  const testUser = user || {
    id: 'test-user-123',
    email: '<EMAIL>',
    name: 'Test User',
  }
  
  return {
    userId: testUser.id,
    prisma: require('../helpers/db-helpers').testPrisma,
    req: {
      headers: {},
      url: 'http://localhost:3000/trpc',
    },
  }
}

/**
 * Create test context for unauthenticated requests
 */
export function createUnauthenticatedTestContext() {
  return {
    userId: null,
    prisma: require('../helpers/db-helpers').testPrisma,
    req: {
      headers: {},
      url: 'http://localhost:3000/trpc',
    },
  }
}
